package test;

import Equipment.Equip;
import model.PlayerInfo;
import shop.ShopCoin;
import java.util.Vector;

/**
 * Test class for ShopCoin functionality
 * Tests that ShopCoin only uses coin currency
 */
public class ShopCoinTest {
    
    public static void testShopCoinBasicFunctionality() {
        System.out.println("Testing ShopCoin basic functionality...");
        
        // Create a test ShopCoin instance
        ShopCoin shopCoin = new ShopCoin();
        
        // Create test items with coin prices
        Vector<Equip> testItems = new Vector<>();
        
        // Create test equipment with coin price
        Equip testEquip1 = new Equip();
        testEquip1.id = 1;
        testEquip1.name = "Test Weapon";
        testEquip1.coin = 100;
        testEquip1.xu = -1;  // No xu price
        testEquip1.luong = -1;  // No luong price
        testEquip1.date = 7;
        testEquip1.strDetail = "Test weapon description";
        testItems.addElement(testEquip1);
        
        Equip testEquip2 = new Equip();
        testEquip2.id = 2;
        testEquip2.name = "Test Armor";
        testEquip2.coin = 200;
        testEquip2.xu = -1;
        testEquip2.luong = -1;
        testEquip2.date = 30;
        testEquip2.strDetail = "Test armor description";
        testItems.addElement(testEquip2);
        
        // Set items to shop
        shopCoin.setItems(testItems);
        
        // Test that items are loaded correctly
        if (shopCoin.items.size() == 2) {
            System.out.println("✓ Items loaded correctly: " + shopCoin.items.size() + " items");
        } else {
            System.out.println("✗ Items not loaded correctly");
        }
        
        // Test that current equipment uses coin
        Equip currentEquip = shopCoin.getCurrEq();
        if (currentEquip != null && currentEquip.coin > 0) {
            System.out.println("✓ Current equipment has coin price: " + currentEquip.coin);
        } else {
            System.out.println("✗ Current equipment doesn't have coin price");
        }
        
        System.out.println("ShopCoin test completed.\n");
    }

    public static void testExpandDetailFunctionality() {
        System.out.println("Testing ShopCoin expand detail functionality...");

        ShopCoin shopCoin = new ShopCoin();

        // Test initial expand state
        if (!shopCoin.expandDetail) {
            System.out.println("✓ Initial expand state is false");
        } else {
            System.out.println("✗ Initial expand state should be false");
        }

        // Test expand toggle
        shopCoin.expandDetail = !shopCoin.expandDetail;
        if (shopCoin.expandDetail) {
            System.out.println("✓ Expand detail toggle works");
        } else {
            System.out.println("✗ Expand detail toggle failed");
        }

        System.out.println("Expand detail test completed.\n");
    }
    
    public static void testPlayerInfoCoinAttribute() {
        System.out.println("Testing PlayerInfo coin attribute...");
        
        PlayerInfo player = new PlayerInfo();
        player.coin = 1000;
        player.xu = 500;
        player.luong = 100;
        
        if (player.coin == 1000) {
            System.out.println("✓ PlayerInfo coin attribute works correctly: " + player.coin);
        } else {
            System.out.println("✗ PlayerInfo coin attribute not working");
        }
        
        System.out.println("PlayerInfo test completed.\n");
    }
    
    public static void main(String[] args) {
        System.out.println("=== ShopCoin Test Suite ===\n");
        
        testPlayerInfoCoinAttribute();
        testShopCoinBasicFunctionality();
        testExpandDetailFunctionality();

        System.out.println("=== All tests completed ===");
    }
}
